camera_enable_para:
  monitor_node: 0
  rtmp_node: 0
  convert_node: 0
  encode_node: 1
  decode_node: 1
  rtsp_push_node: 1
  rtsp_pull_node: 0
  mp4_save_node: 0
  image_radar_align_node: 1
camera_common_para:
  device_position_flag: 2                                   # 标识自身是哪个设备 0:主设备0, 1:从设备1, 2:从设备2, 3:从设备3
  data_playback_enable: 0
  target_device_ip: "************"                          # 目标设备ip
  framerate: 30
  framerate_output: 30
  io_method: "userptr"
  av_device_format: "YUV422P"                               # output format
  use_camera_time: false                                    # true: use camera trigger time; false: use local_time - delay;
cam_r_para:
  cam_enable: 1                                             # cam 使能
  cam_dedistort_enable: 1                                   # cam 去畸变使能
  cam_post_fusion_enable: 2                                 # 0:后融合 1:前融合 2:前后融合同时开启
  cam_video_enable_flag: 1                                  # 录像使能
  cam_live_enable_flag: 1                                   # 直播使能
  cam_rgb_reserve_enable: 0                                 # 预留路，具备订阅后发布编码功能
  cam_license_spot_enable: 0                                # 车牌识别使能
  image_width: 3840                                         # CamR 原图 长
  image_height: 2160                                        # CamR 原图 宽
  cam_crop_width: 3840                                      # CamR 裁切 长
  cam_crop_height: 2160                                     # CamR 裁切 宽
  cam_crop_position: 0                                      # CamR 裁切 起始高度
  cam_origin_image_width: 640                               # CamR 原图 长
  cam_origin_image_height: 360                              # CamR 原图 宽
  cam_video_image_width: 1920                               # CamR 录像 长
  cam_video_image_height: 1080                              # CamR 录像 宽
  cam_live_image_width: 1920                                # CamR 直播 长
  cam_live_image_height: 1080                               # CamR 直播 宽
  cam_origin_encode_framerate: 30                           # 原图帧率 (注意此帧率需低于相机帧率)
  cam_video_encode_framerate: 30                            # 录像帧率
  cam_live_encode_framerate: 30                             # 直播帧率 
  cam_reserve_encode_framerate: 30                          # 直播帧率 
  # 前融合\录像\直播 i420 发布 topic name
  cam_i420_pre_fusion: "CamR/pre_fusion_i420"               # CamR I420 前融合发布topic name
  cam_i420_video: "CamR/video_i420"                         # CamR I420 录像发布topic name
  cam_i420_live: "CamR/live_i420"                           # CamR I420 直播发布topic name
  cam_rgb_post_fusion: "CamR/rgb"                           # CamR 后融合rgb topic name
  cam_i420_lisence_spot: "CamR/lisence_spot"                # CamR 车牌识别 topic name
  cam_status: "CamR/monitor"                                # Cam 监控发布topic name
  # 图像发布订阅 topic name /预留yolo或车牌识别
  cam_rgb_reserve: "CamR/rgb_reserve"                       # CamR RGB 预留图像发布 topic name
  cam_rgb_reserve_dealed: "CamR/reserve_dealed"             # CamR RGB 预留处理后订阅 topic name
  cam_i420_reserve: "CamR/i420_reserve"                     # CamR i420 预留处理后发布 topic name
  device_name: "/dev/video0"                                # 设备名
  camera_name: "ox08b40_60h"                                # 相机名称
  frame_id: "ox08b40_60h"                                   # 相机名称
  pixel_format: "yuyv2rgb"                                  # see usb_cam/supported_formats for list of supported formats
  delay_estimate: 90                                        # time delay of camera transform  
  encode_rgb8: "rgb8"                                       # 编码格式
  encode_yuv: "yuv420"                                      # 编码格式
  # 编码后图像 发布 topic name
  origin_image_encode: "CamR/origin_image_encode"           # CamR H265 360P发布topic name
  video_encode: "CamR/video_encode"                         # CamR H265 1080P发布topic name
  live_encode: "CamR/live_encode"                           # CamR H264 720P发布topic name
  reserve_encode: "CamR/reserve_encode"                     # CamR H264/H265 预留路发布topic name
  # 编码码率
  origin_image_bit: 2000000                                 # 码率2M 30fps,4k:16M,1080p:8M,720p:4M
  video_bit: 16000000                                       # 码率16M
  live_bit: 4000000                                         # 码率4M
  reserve_bit: 4000000                                      # 码率4M
  # 编码参数
  origin_encode_profile: 3                                  # 0：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN             # 1：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN10
                                                            # 2：V4L2_MPEG_VIDEO_H265_PROFILE_MAINSTILLPICTURE # 3：V4L2_MPEG_VIDEO_H265_PROFILE_FREXT
  video_encode_profile: 3
  origin_encode_speed: 1                                    # 0：V4L2_ENC_HW_PRESET_DISABLE # 1：V4L2_ENC_HW_PRESET_ULTRAFAST # 2：V4L2_ENC_HW_PRESET_FAST
                                                            # 3：V4L2_ENC_HW_PRESET_MEDIUM  # 4：V4L2_ENC_HW_PRESET_SLOW
  video_encode_speed: 1
  live_encode_speed: 1
  
  #编码帧间隔
  origin_iframe_interval: 30                                # IFRAME 间隔
  video_iframe_interval: 30
  live_iframe_interval: 30
  origin_idr_interval: 30                                   # IDR 间隔
  video_idr_interval: 30
  live_idr_interval: 30
  #编码socket端口
  socket_device_0_port: 1931                                # cam_a socket端口
  socket_device_1_port: 1934                                # cam_b socket端口
  socket_device_2_port: 1937                                # cam_c socket端口

  #编码rtsp推流地址
  video_rtsp_push_url: "rtsp://127.0.0.1:3935/camr_video/stream"  # CamR Rtsp 录像推送地址
  live_rtsp_push_url: "rtsp://127.0.0.1:3935/camr_live/stream"    # CamR Rtsp 直播推送地址

  decode_device_enable: 0x1000

  # 数据回放图像发布topic name
  data_play_device_0: "CamR_Device_0/Data_Playback"         # CamR 数据回放发布topic name
  data_play_device_1: "CamR_Device_1/Data_Playback"         # CamR 数据回放发布topic name
  data_play_device_2: "CamR_Device_2/Data_Playback"         # CamR 数据回放发布topic name
  data_play_device_3: "CamR_Device_3/Data_Playback"         # CamR 数据回放发布topic name

  # mp4 录制参数
  record_time: 1                                            # mp4 录制时长
  file_name: "TYJT_CAMR"

cam_l_para:
  cam_enable: 1                                             # cam 使能
  cam_dedistort_enable: 1                                   # cam 去畸变使能
  cam_post_fusion_enable: 2                                 # 0:后融合 1:前融合 2:前后融合同时开启
  cam_video_enable_flag: 1                                  # 录像使能
  cam_live_enable_flag: 1                                   # 直播使能
  cam_rgb_reserve_enable: 0                                 # 预留路，具备订阅后发布编码功能
  cam_license_spot_enable: 0                                # 车牌识别使能
  image_width: 1920                                         # CamL 原图 长
  image_height: 1536                                        # CamL 原图 宽
  cam_crop_width: 1920                                      # CamL 裁切 长
  cam_crop_height: 1080                                     # CamL 裁切 宽
  cam_crop_position: 4                                      # CamL 裁切 起始高度
  cam_origin_image_width: 640                               # CamL 原图 长
  cam_origin_image_height: 360                              # CamL 原图 宽
  cam_video_image_width: 1920                               # CamL 录像 长
  cam_video_image_height: 1536                              # CamL 录像 宽
  cam_live_image_width: 1920                                # CamL 直播 长
  cam_live_image_height: 1536                               # CamL 直播 宽
  cam_origin_encode_framerate: 30                           # 原图帧率 (注意此帧率需低于相机帧率)
  cam_video_encode_framerate: 30                            # 录像帧率
  cam_live_encode_framerate: 30                             # 直播帧率 
  # 前融合\录像\直播 i420 发布 topic name
  cam_i420_pre_fusion: "CamL/pre_fusion_i420"               # CamL I420 前融合发布topic name
  cam_i420_video: "CamL/video_i420"                         # CamL I420 录像发布topic name
  cam_i420_live: "CamL/live_i420"                           # CamL I420 直播发布topic name
  cam_rgb_post_fusion: "CamL/rgb"                           # CamL 后融合rgb topic name
  cam_i420_lisence_spot: "CamL/lisence_spot"                # CamL 车牌识别 topic name
  cam_status: "CamL/monitor"                                # CamL 监控发布topic name
  # 图像发布订阅 topic name /预留yolo或车牌识别
  cam_rgb_reserve: "CamL/rgb_reserve"                       # CamL RGB 预留图像发布 topic name
  cam_rgb_reserve_dealed: "CamL/reserve_dealed"             # CamL RGB 预留处理后订阅 topic name
  cam_i420_reserve: "CamL/i420_reserve"                     # CamL i420 预留处理后发布 topic name
  device_name: "/dev/video1"                                # 设备名
  camera_name: 'isx031_30h'                                 # 相机名称
  frame_id: "isx031_30h"                                    # 相机名称
  pixel_format: "yuyv2rgb"                                  # see usb_cam/supported_formats for list of supported formats
  delay_estimate: 80                                        # time delay of camera transform
  encode_rgb8: "rgb8"                                       # 编码格式
  encode_yuv: "yuv420"                                      # 编码格式
  # 编码后图像 发布 topic name
  origin_image_encode: "CamL/origin_image_encode"           # CamL H265 360P发布topic name
  video_encode: "CamL/video_encode"                         # CamL H265 1080P发布topic name
  live_encode: "CamL/live_encode"                           # CamL H264/H265 1080P发布topic name
  # 编码码率
  origin_image_bit: 2000000                                 # 码率2M 30fps,4k:16M,1080p:8M,720p:4M
  video_bit: 16000000                                       # 码率4M
  live_bit: 4000000                                         # 码率2M
  # 编码参数
  origin_encode_profile: 3                                  # 0：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN             # 1：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN10
                                                            # 2：V4L2_MPEG_VIDEO_H265_PROFILE_MAINSTILLPICTURE # 3：V4L2_MPEG_VIDEO_H265_PROFILE_FREXT
  video_encode_profile: 3
  # live_encode_profile: 3
  origin_encode_speed: 1                                    # 0：V4L2_ENC_HW_PRESET_DISABLE # 1：V4L2_ENC_HW_PRESET_ULTRAFAST # 2：V4L2_ENC_HW_PRESET_FAST
                                                            # 3：V4L2_ENC_HW_PRESET_MEDIUM  # 4：V4L2_ENC_HW_PRESET_SLOW
  video_encode_speed: 1
  live_encode_speed: 1
  origin_iframe_interval: 30                                # IFRAME 间隔
  video_iframe_interval: 30
  live_iframe_interval: 30
  origin_idr_interval: 30                                   # IDR 间隔
  video_idr_interval: 30
  live_idr_interval: 30

  socket_device_0_port: 1932                                # cam_a socket端口
  socket_device_1_port: 1935                                # cam_b socket端口
  socket_device_2_port: 1938                                # cam_c socket端口

  video_rtsp_push_url: "rtsp://127.0.0.1:3935/caml_video/stream"  # CamR Rtsp 录像推送地址
  live_rtsp_push_url: "rtsp://127.0.0.1:3935/caml_live/stream"    # CamR Rtsp 直播推送地址
  
  decode_device_enable: 0x1000

  # 数据回放图像发布topic name
  data_play_device_0: "CamL_Device_0/Data_Playback"           # CamL 数据回放发布topic name
  data_play_device_1: "CamL_Device_1/Data_Playback"           # CamL 数据回放发布topic name
  data_play_device_2: "CamL_Device_2/Data_Playback"           # CamL 数据回放发布topic name
  data_play_device_3: "CamL_Device_3/Data_Playback"           # CamL 数据回放发布topic name

  # mp4 录制参数
  record_time: 1                                              # mp4 录制时长
  file_name: "TYJT_CAML"

cam_b_para:
  cam_enable: 1                                             # cam 使能
  cam_dedistort_enable: 0                                   # cam 去畸变使能
  cam_post_fusion_enable: 2                                 # 0:后融合 1:前融合 2:前后融合同时开启
  cam_video_enable_flag: 1                                  # 录像使能
  cam_live_enable_flag: 1                                   # 直播使能
  cam_rgb_reserve_enable: 0                                 # 预留路，具备订阅后发布编码功能
  cam_license_spot_enable: 0                                # 车牌识别使能
  image_width: 1920                                         # CamB 原图 长
  image_height: 1536                                        # CamB 原图 宽
  cam_crop_width: 1920                                      # CamB 裁切 长
  cam_crop_height: 1080                                     # CamB 裁切 宽
  cam_crop_position: 4                                      # CamB 裁切 起始高度
  cam_origin_image_width: 640                               # CamB 原图 长
  cam_origin_image_height: 360                              # CamB 原图 宽
  cam_video_image_width: 1920                               # CamB 录像 长
  cam_video_image_height: 1536                              # CamB 录像 宽
  cam_live_image_width: 1920                                # CamB 直播 长    
  cam_live_image_height: 1536                               # CamB 直播 宽
  cam_origin_encode_framerate: 30                           # 原图帧率 (注意此帧率需低于相机帧率)
  cam_video_encode_framerate: 30                            # 录像帧率
  cam_live_encode_framerate: 30                             # 直播帧率 
  # 前融合\录像\直播 i420 发布 topic name
  cam_i420_pre_fusion: "CamB/pre_fusion_i420"               # CamB I420 前融合发布topic name
  cam_i420_video: "CamB/video_i420"                         # CamB I420 录像发布topic name
  cam_i420_live: "CamB/live_i420"                           # CamB I420 直播发布topic name
  cam_rgb_post_fusion: "CamB/rgb"                           # CamB 后融合rgb topic name
  cam_i420_lisence_spot: "CamB/lisence_spot"                # CamB 车牌识别 topic name
  cam_status: "CamB/monitor"                                # CamB 监控发布topic name
  # 图像发布订阅 topic name /预留yolo或车牌识别
  cam_rgb_reserve: "CamB/rgb_reserve"                       # CamB RGB 图像发布 topic name
  cam_rgb_reserve_dealed: "CamB/reserve_dealed"             # CamB RGB yolo处理后订阅 topic name
  cam_i420_reserve: "CamB/i420_reserve"                     # CamB i420 yolo处理后发布 topic name
  device_name: "/dev/video2"                                # 设备名
  camera_name: 'isx031_195h'                                # 相机名称
  frame_id: "isx031_195h"                                   # 相机名称
  pixel_format: "yuyv2rgb"                                  # see usb_cam/supported_formats for list of supported formats
  delay_estimate: 80                                        # time delay of camera transform
  encode_rgb8: "rgb8"                                       # 编码格式
  encode_yuv: "yuv420"                                      # 编码格式
  # 编码后图像 发布 topic name
  origin_image_encode: "CamB/origin_image_encode"           # CamB H264 720P发布topic name
  video_encode: "CamB/video_encode"                         # CamB H264 1080P发布topic name
  live_encode: "CamB/live_encode"                           # CamB H264 4k发布topic name
  # 编码码率
  origin_image_bit: 2000000                                 # 码率2M
  video_bit: 16000000                                       # 码率 30fps,4k:8M,1080p:4M,720p:2M
  live_bit: 4000000                                         # 码率2M
  # 编码参数
  origin_encode_profile: 3                                  # 0：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN             # 1：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN10
                                                            # 2：V4L2_MPEG_VIDEO_H265_PROFILE_MAINSTILLPICTURE # 3：V4L2_MPEG_VIDEO_H265_PROFILE_FREXT
  video_encode_profile: 3
  # live_encode_profile: 3
  origin_encode_speed: 1                                    # 0：V4L2_ENC_HW_PRESET_DISABLE # 1：V4L2_ENC_HW_PRESET_ULTRAFAST # 2：V4L2_ENC_HW_PRESET_FAST
                                                            # 3：V4L2_ENC_HW_PRESET_MEDIUM  # 4：V4L2_ENC_HW_PRESET_SLOW
  video_encode_speed: 1
  live_encode_speed: 1
  origin_iframe_interval: 30
  video_iframe_interval: 30
  live_iframe_interval: 30
  origin_idr_interval: 30
  video_idr_interval: 30
  live_idr_interval: 30

  socket_device_0_port: 1933                                # cam_a 端口
  socket_device_1_port: 1936                                # cam_b 端口
  socket_device_2_port: 1939                                # cam_c 端口

  video_rtsp_push_url: "rtsp://127.0.0.1:3935/camb_video/stream"
  live_rtsp_push_url: "rtsp://127.0.0.1:3935/camb_live/stream" 

  decode_device_enable: 0x1000

  # 数据回放图像发布topictopic name
  data_play_device_0: "CamB_Device_0/Data_Playback"          # CamB 数据回放发布topic name
  data_play_device_1: "CamB_Device_1/Data_Playback"          # CamB 数据回放发布topic name
  data_play_device_2: "CamB_Device_2/Data_Playback"          # CamB 数据回放发布topic name   
  data_play_device_3: "CamB_Device_3/Data_Playback"          # CamB 数据回放发布topic name  

  # mp4 录制参数
  record_time: 1                                              # mp4 录制时长
  file_name: "TYJT_CAMB"

cam_rtsp_pull_para:
  pull_stream_num: 8                                          # 最大支持12路，开启此模式需要打开数据回放(data_playback_enable)功能 
  # RTSP PULL 节点拉流地址
  rtsp_pull_0_url: "rtsp://admin:@Tyjt101@************:554/Streaming/Channels/102"
  rtsp_pull_1_url: "udp://localhost:1935/CamL"
  rtsp_pull_2_url: "udp://localhost:2935/CamB"
  rtsp_pull_3_url: "udp://localhost:3935/CamR"
  rtsp_pull_4_url: "udp://localhost:4935/CamL"
  rtsp_pull_5_url: "udp://localhost:5935/CamB"
  rtsp_pull_6_url: "udp://localhost:6935/CamR"
  rtsp_pull_7_url: "udp://localhost:7935/CamL"
  rtsp_pull_8_url: "udp://localhost:4935/CamL"
  rtsp_pull_9_url: "udp://localhost:5935/CamB"
  rtsp_pull_10_url: "udp://localhost:6935/CamR"
  rtsp_pull_11_url: "udp://localhost:7935/CamL"
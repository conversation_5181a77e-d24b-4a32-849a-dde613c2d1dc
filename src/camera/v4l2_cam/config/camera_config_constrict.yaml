camera_enable_para:
  monitor_node: 1
  rtmp_node: 0
  convert_node: 0
  encode_node: 0
  decode_node: 1
  rtsp_push_node: 0
  rtsp_pull_node: 1
  sip_server_node: 0
  mp4_save_node: 0
  image_radar_align_node: 0
camera_common_para:
  device_position_flag: 0                                   # 标识自身是哪个设备 0:主设备0, 1:从设备1, 2:从设备2, 3:从设备3
  data_playback_enable: 1
  target_device_ip: "**************"                        # 目标设备ip
 
cam_r_para:
  cam_enable: 0                                             # cam 使能
  cam_dedistort_enable: 1                                   # cam 去畸变使能
  cam_post_fusion_enable: 2                                 # 0:后融合 1:前融合 2:前后融合同时开启
  cam_video_enable_flag: 1                                  # 录像使能
  cam_live_enable_flag: 0                                   # 直播使能
  cam_rgb_reserve_enable: 0                                 # 预留路，具备订阅后发布编码功能
  cam_license_spot_enable: 0                                # 车牌识别使能
  image_width: 3840                                         # CamR 原图 长
  image_height: 2160                                        # CamR 原图 宽
  cam_crop_width: 3840                                      # CamR 裁切 长
  cam_crop_height: 2160                                     # CamR 裁切 宽
  cam_crop_position: 0                                      # CamR 裁切 起始高度
  cam_origin_image_width: 640                               # CamR 原图 长
  cam_origin_image_height: 360                              # CamR 原图 宽
  cam_video_image_width: 1920                               # CamR 录像 长
  cam_video_image_height: 1080                              # CamR 录像 宽
  cam_live_image_width: 1920                                # CamR 直播 长
  cam_live_image_height: 1080                               # CamR 直播 宽
  cam_origin_encode_framerate: 10                           # 原图帧率 (注意此帧率需低于相机帧率)
  cam_video_encode_framerate: 30                            # 录像帧率
  cam_live_encode_framerate: 30                             # 直播帧率 
  cam_reserve_encode_framerate: 30                          # 直播帧率 

  # 编码码率
  origin_image_bit: 2000000                                 # 码率2M 30fps,4k:16M,1080p:8M,720p:4M
  video_bit: 16000000                                       # 码率16M
  live_bit: 4000000                                         # 码率4M
  reserve_bit: 4000000                                      # 码率4M
  # 编码参数
  origin_encode_profile: 3                                  # 0：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN             # 1：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN10
                                                            # 2：V4L2_MPEG_VIDEO_H265_PROFILE_MAINSTILLPICTURE # 3：V4L2_MPEG_VIDEO_H265_PROFILE_FREXT
  video_encode_profile: 3
  origin_encode_speed: 1                                    # 0：V4L2_ENC_HW_PRESET_DISABLE # 1：V4L2_ENC_HW_PRESET_ULTRAFAST # 2：V4L2_ENC_HW_PRESET_FAST
                                                            # 3：V4L2_ENC_HW_PRESET_MEDIUM  # 4：V4L2_ENC_HW_PRESET_SLOW
  video_encode_speed: 1
  live_encode_speed: 1

  decode_device_enable: 0x1111                              # 解码使能哪一路 0x0000不使能, 0x1000使能第一台设备, 0x0100使能第二台设备，0x1100使能第一台设备和第二台设备，依次类推

cam_l_para:
  cam_enable: 0                                             # cam 使能
  cam_dedistort_enable: 1                                   # cam 去畸变使能
  cam_post_fusion_enable: 2                                 # 0:后融合 1:前融合 2:前后融合同时开启
  cam_video_enable_flag: 0                                  # 录像使能
  cam_live_enable_flag: 0                                   # 直播使能
  cam_rgb_reserve_enable: 0                                 # 预留路，具备订阅后发布编码功能
  cam_license_spot_enable: 0                                # 车牌识别使能
  image_width: 1920                                         # CamL 原图 长
  image_height: 1536                                        # CamL 原图 宽
  cam_crop_width: 1920                                      # CamL 裁切 长
  cam_crop_height: 1080                                     # CamL 裁切 宽
  cam_crop_position: 0                                      # CamL 裁切 起始高度
  cam_origin_image_width: 640                               # CamL 原图 长
  cam_origin_image_height: 360                              # CamL 原图 宽
  cam_video_image_width: 1920                               # CamL 录像 长
  cam_video_image_height: 1536                              # CamL 录像 宽
  cam_live_image_width: 1920                                # CamL 直播 长
  cam_live_image_height: 1536                               # CamL 直播 宽
  cam_origin_encode_framerate: 10                           # 原图帧率 (注意此帧率需低于相机帧率)
  cam_video_encode_framerate: 30                            # 录像帧率
  cam_live_encode_framerate: 30                             # 直播帧率 

  # 编码码率
  origin_image_bit: 2000000                                 # 码率2M 30fps,4k:16M,1080p:8M,720p:4M
  video_bit: 16000000                                       # 码率4M
  live_bit: 4000000                                         # 码率2M
  # 编码参数
  origin_encode_profile: 3                                  # 0：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN             # 1：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN10
                                                            # 2：V4L2_MPEG_VIDEO_H265_PROFILE_MAINSTILLPICTURE # 3：V4L2_MPEG_VIDEO_H265_PROFILE_FREXT
  video_encode_profile: 3
  origin_encode_speed: 1                                    # 0：V4L2_ENC_HW_PRESET_DISABLE # 1：V4L2_ENC_HW_PRESET_ULTRAFAST # 2：V4L2_ENC_HW_PRESET_FAST
                                                            # 3：V4L2_ENC_HW_PRESET_MEDIUM  # 4：V4L2_ENC_HW_PRESET_SLOW
  video_encode_speed: 1
  live_encode_speed: 1

  decode_device_enable: 0x0000

cam_b_para:
  cam_enable: 0                                             # cam 使能
  cam_dedistort_enable: 0                                   # cam 去畸变使能
  cam_post_fusion_enable: 2                                 # 0:后融合 1:前融合 2:前后融合同时开启
  cam_video_enable_flag: 0                                  # 录像使能
  cam_live_enable_flag: 0                                   # 直播使能
  cam_rgb_reserve_enable: 0                                 # 预留路，具备订阅后发布编码功能
  cam_license_spot_enable: 0                                # 车牌识别使能
  image_width: 1920                                         # CamB 原图 长
  image_height: 1536                                        # CamB 原图 宽
  cam_crop_width: 1920                                      # CamB 裁切 长
  cam_crop_height: 1080                                     # CamB 裁切 宽
  cam_crop_position: 0                                      # CamB 裁切 起始高度
  cam_origin_image_width: 640                               # CamB 原图 长
  cam_origin_image_height: 360                              # CamB 原图 宽
  cam_video_image_width: 1920                               # CamB 录像 长
  cam_video_image_height: 1536                              # CamB 录像 宽
  cam_live_image_width: 1920                                # CamB 直播 长    
  cam_live_image_height: 1536                               # CamB 直播 宽
  cam_origin_encode_framerate: 10                           # 原图帧率 (注意此帧率需低于相机帧率)
  cam_video_encode_framerate: 30                            # 录像帧率
  cam_live_encode_framerate: 30                             # 直播帧率 

  # 编码码率
  origin_image_bit: 2000000                                 # 码率2M
  video_bit: 16000000                                       # 码率 30fps,4k:8M,1080p:4M,720p:2M
  live_bit: 4000000                                         # 码率2M
  # 编码参数
  origin_encode_profile: 0                                  # 0：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN             # 1：V4L2_MPEG_VIDEO_H265_PROFILE_MAIN10
                                                            # 2：V4L2_MPEG_VIDEO_H265_PROFILE_MAINSTILLPICTURE # 3：V4L2_MPEG_VIDEO_H265_PROFILE_FREXT
  video_encode_profile: 3
  origin_encode_speed: 1                                    # 0：V4L2_ENC_HW_PRESET_DISABLE # 1：V4L2_ENC_HW_PRESET_ULTRAFAST # 2：V4L2_ENC_HW_PRESET_FAST
                                                            # 3：V4L2_ENC_HW_PRESET_MEDIUM  # 4：V4L2_ENC_HW_PRESET_SLOW
  video_encode_speed: 1
  live_encode_speed: 1

  decode_device_enable: 0x0000

cam_rtsp_pull_para:
  pull_stream_num: 4                                        # 最大支持12路，开启此模式需要打开数据回放(data_playback_enable)功能
  # RTSP PULL 节点拉流地址
  camera_0_type: 0                                          # 相机类型：天翼:0, 海康:1, 大华:2, 宇视:3
  rtsp_pull_0_url: "rtsp://admin:@Tyjt101@************:554/Streaming/Channels/102"
  camera_1_type: 0
  rtsp_pull_1_url: "rtsp://admin:@Tyjt101@*************:554/Streaming/Channels/102"
  camera_2_type: 0
  rtsp_pull_2_url: "rtsp://admin:@Tyjt101@************:554/Streaming/Channels/102"
  camera_3_type: 0
  rtsp_pull_3_url: "rtsp://admin:@Tyjt101@*************:554/Streaming/Channels/102"
  camera_4_type: 0
  rtsp_pull_4_url: "rtsp://**************:3935/camr_video/stream"
  camera_5_type: 0
  rtsp_pull_5_url: "udp://localhost:5935/CamB"
  camera_6_type: 0
  rtsp_pull_6_url: "udp://localhost:6935/CamR"
  camera_7_type: 0
  rtsp_pull_7_url: "udp://localhost:7935/CamL"
  camera_8_type: 0
  rtsp_pull_8_url: "udp://localhost:4935/CamL"
  camera_9_type: 0
  rtsp_pull_9_url: "udp://localhost:5935/CamB"
  camera_10_type: 0
  rtsp_pull_10_url: "udp://localhost:6935/CamR"
  camera_11_type: 0
  rtsp_pull_11_url: "udp://localhost:7935/CamL"

sip_server_para:
  ua: "SipServer"
  nonce: "1234567890123456"                                 # SIP服务随机数值
  ip: "************"                                        # SIP服务IP
  port: 5060                                                # SIP服务端口
  rtp_port: 10000                                           # SIP服务端口
  sip_id: "34020000002000000001"                            # SIP服务器ID
  sip_realm: "3402000000"                                   # SIP服务器域
  sip_pass: "123456"                                        # SIP password
  sip_timeout: 1800                                         # SIP timeout
  sip_expiry: 3600                                          # SIP 到期
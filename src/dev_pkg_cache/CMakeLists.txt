cmake_minimum_required(VERSION 3.8)
project(dev_pkg_cache LANGUAGES CXX CUDA)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17 -O3")
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_BUILD_TYPE Release)
option(CUDA_USE_STATIC_CUDA_RUNTIME OFF)

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# =========================== 常规依赖设置==================================================
option(CMAKE_CUDASM "CUDA SM Version" OFF)
set(CMAKE_CUDASM 87)
set(ENV{ConfigurationStatus} "Failed")
# Set TensorRT-related variables
set(ENV{TensorRT_Lib} "/usr/lib/aarch64-linux-gnu")
set(ENV{TensorRT_Inc} "/usr/include/aarch64-linux-gnu/")
set(ENV{TensorRT_Bin} "/usr/src/tensorrt/bin/")
# Set CUDA-related variables
set(ENV{CUDA_Lib} "/usr/local/cuda12.2/lib64")
set(ENV{CUDA_Inc} "/usr/local/cuda12.2/include")
set(ENV{CUDA_Bin} "/usr/local/cuda12.2/bin")
set(ENV{CUDNN_Lib} "/usr/local/cuda12.2/lib64")
# Set protobuf and spconv variables
# set(ENV{Protobuf_Lib} ${CMAKE_CURRENT_SOURCE_DIR}/devel/lib)
# set(ENV{Spdlog_Lib} ${CMAKE_CURRENT_SOURCE_DIR}/devel/lib)
# set(ENV{Spconv_Lib} ${CMAKE_CURRENT_SOURCE_DIR}/devel/lib)
# # set lib
# set(OpenCV_DIR "/usr/local/opencv_481/lib/cmake/opencv4")
# set(CUDA_TOOLKIT_ROOT_DIR "/usr/local/cuda-11.4")
set(ENV{PATH} "$ENV{TensorRT_Bin}:$ENV{CUDA_Bin}:$ENV{PATH}")
set(ENV{LD_LIBRARY_PATH} "$ENV{TensorRT_Lib}:$ENV{CUDA_Lib}:$ENV{CUDNN_Lib}:$ENV{Spconv_Lib}:$ENV{LD_LIBRARY_PATH}")
set(ENV{ConfigurationStatus} "Success")

set(CUDA_NVCC_FLAGS_RELEASE "-Xcompiler -std=c++14,-Wextra,-Wall,-Wno-deprecated-declarations,-O3 -DENABLE_TEXT_BACKEND_STB -use_fast_math")
set(CUDA_NVCC_FLAGS_DEBUG "-std=c++17 -O0 -g -DENABLE_TEXT_BACKEND_STB -use_fast_math")

message(STATUS "Set CUDA optimizaion flag for cuda arc version = `${CMAKE_CUDASM}`")
if(CMAKE_BUILD_TYPE MATCHES "Release")
  set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS_RELEASE}
    -gencode arch=compute_${CMAKE_CUDASM},code=compute_${CMAKE_CUDASM})
elseif(CMAKE_BUILD_TYPE MATCHES "Debug")
  set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS_DEBUG}
    -gencode arch=compute_${CMAKE_CUDASM},code=compute_${CMAKE_CUDASM})
endif()
# =========================================================================================

# Paths
set(SRC_PATH "src")
set(INC_PATH "include/dev_pkg_cache")

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()
# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)
find_package(ament_index_cpp REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(message_filters REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(OpenCV REQUIRED)
find_package(CUDA REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(Protobuf REQUIRED)

# Include directories
include_directories(
    ${SRC_PATH}
    ${INC_PATH}
    ${OpenCV_INCLUDE_DIRS}
    ${CUDA_INCLUDE_DIR}
    # Do not need to set trt inc in trt docker

    ${Protobuf_INCLUDE_DIRS}
)

# Libraries
link_directories(
    ${CUDA_LIB_DIR}
    # Do not need to set trt lib in trt docker
)


# Add the vehicle detection detr component library
add_library(ros2_save_image_component_test SHARED ${SRC_PATH}/ros2_save_image_component_test.cpp)
target_compile_features(ros2_save_image_component_test PUBLIC cxx_std_17)
target_link_libraries(ros2_save_image_component_test
    ${Protobuf_LIBRARIES}
    rt
    pthread
)
ament_target_dependencies(ros2_save_image_component_test
    rclcpp
    rclcpp_components
    sensor_msgs
    std_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Register the component
rclcpp_components_register_nodes(ros2_save_image_component_test "CacheWorker::SaveImageNode")
install(TARGETS ros2_save_image_component_test
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)


# Install launch directory for dev_pkg_det2d
install(DIRECTORY launch
    DESTINATION share/${PROJECT_NAME}
)


if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()

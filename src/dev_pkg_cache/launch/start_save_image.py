import time
import threading

import subprocess
from launch.actions import DeclareLaunchArgument, ExecuteProcess

from launch import LaunchDescription  # 导入Launch描述
# from launch.actions import GroupAction  # noqa: E402
from launch_ros.actions import ComposableNodeContainer
# from launch_ros.actions import Node  # noqa: E402
from launch_ros.descriptions import ComposableNode
import os
import yaml

from launch.actions import DeclareLaunchArgument, ExecuteProcess
from launch.actions import RegisterEventHandler, EmitEvent
from launch.event_handlers import OnProcessExit
from launch.events import Shutdown

cpc_name = os.environ.get('CPC_NAME', '')
print("cpc_name=" + cpc_name)
cpc_group_name = os.environ.get('GROUP_NAME', '')
# log_level = 'info'
log_level = 'error'


config_path_para = {
    'config_path':"src/camera/v4l2_cam/config/camera_config.yaml",
    'config_path_constrict':"src/camera/v4l2_cam/config/camera_config_constrict.yaml",
    'config_path_pull_decode':"src/camera/v4l2_cam/config/camera_config_pull_decode.yaml",
    'camera_intrinsic_path' : "/docker_config"
}

cam_r_para={
    'device_name': "/dev/video0"                          # 设备名    
}
cam_l_para={
    'device_name': "/dev/video1"                          # 设备名   
}
cam_b_para={
    'device_name': "/dev/video2"                          # 设备名
}


# RTMP 节点配置参数
if os.path.exists('/docker_config/rtmp_addr.yaml'):
    with open('/docker_config/rtmp_addr.yaml', 'r') as file:
        rtmp_addr = yaml.safe_load(file)
    rtmp_addr['rtmp_url'] += cpc_name + '_'
else:
    rtmp_addr = {'rtmp_url' : 'rtmp://127.0.0.1:2935/live/'}

print(rtmp_addr['rtmp_url'])


# RTSP RTMP 节点配置参数
cam_rtsp_rtmp_para={
    # RTMP 节点使能
    'camr_rtmp_enable':1,                                     # camr rtmp 使能
    'caml_rtmp_enable':1,                                     # caml rtmp 使能
    'camb_rtmp_enable':1,                                     # camb rtmp 使能 
    # RTMP 节点推流地址
    'camr_rtmp_url': rtmp_addr['rtmp_url'] + 'CamR',
    'caml_rtmp_url': rtmp_addr['rtmp_url'] + 'CamL',
    'camb_rtmp_url': rtmp_addr['rtmp_url'] + 'CamB', 
}

# 记录初始的修改时间
def get_file_mtime(path):
    return os.stat(path).st_mtime if os.path.exists(path) else None

config_mtime = {path: get_file_mtime(path) for path in config_path_para.values()}
config_files = list(config_path_para.values())

restart_flag = False

def restart_ros2_nodes():
    global restart_flag
    if restart_flag:
        return
    restart_flag = True

    # 强制终止所有相关进程
    os.system("pkill -f component_container_mt")
    time.sleep(5)  # 等待清理

    print("[INFO] 重新启动 ROS 2 组件...")
    try:
        # 以非阻塞方式启动 ROS 2 节点
        subprocess.call(["ros2", "launch", "usb_cam", "start_road.py"])
        print("[INFO] ROS 2 组件已重新启动")
    except Exception as e:
        print(f"[ERROR] 启动失败: {e}")
    restart_flag = False

def monitor_config_change():
    global config_mtime
    while True:
        time.sleep(5)
        need_restart = False
        for path in config_files:
            current_mtime = get_file_mtime(path)
            if current_mtime and current_mtime != config_mtime.get(path):
                print(f"[INFO] 文件 {path} 已修改")
                config_mtime[path] = current_mtime
                need_restart = True
        if need_restart:
            restart_ros2_nodes()

monitor_thread = threading.Thread(target=monitor_config_change, daemon=True)
monitor_thread.start()
def generate_launch_description():
    """Generate launch description with multiple components."""
    container = ComposableNodeContainer(
        name='usb_cam_container',
        namespace=cpc_name,
        package='rclcpp_components',
        executable='component_container_mt',
        # prefix=['gdbserver localhost:6688'],
        # prefix='xterm -e gdb --args',
        
        composable_node_descriptions=[
            ComposableNode(
                package='usb_cam',
                plugin='usb_cam::Sys_monitor_entrance',
                namespace=cpc_name,
                name = "Sys_monitor_entrance",
                parameters = [
                    config_path_para
                ],
                extra_arguments=[{'use_intra_process_comms': True}]
            ),

            ComposableNode(
                package='usb_cam',
                plugin='usb_cam::Rtsp_pull_entrance',
                # namespace=cpc_name,
                name = "CamR_rtsp_pull",
                parameters = [
                    config_path_para
                ],
                extra_arguments=[{'use_intra_process_comms': True}]
            ),

            ComposableNode(
                package='usb_cam',
                plugin='usb_cam::Decode_jetson_entrance',
                # namespace=cpc_name,
                name = "Decode_jetson_entrance",
                parameters = [
                    config_path_para,
                    {'namespace': cpc_name}
                ],
                extra_arguments=[{'use_intra_process_comms': True}]
            ),

            ComposableNode(
                package='dev_pkg_cache',
                plugin='CacheWorker::SaveImageNode',
                namespace=cpc_name,
                name = "save_image_node",
                parameters = [
                    {
                        'input_topics': ['/device_0_decode', '/device_1_decode', '/device_2_decode', '/device_3_decode'],
                        'max_queue_size': 1,  # 增加队列大小以减少丢帧
                        'save_path': '/ai_police/saved_images',
                        'retention_seconds': 90
                    }
                ],
                extra_arguments=[{'use_intra_process_comms': True}]
            ),
        ],
        output='both'
    )

    event_handler = RegisterEventHandler(
        OnProcessExit(
            target_action=container,
            on_exit=[
                EmitEvent(event=Shutdown(reason='Container exited, restarting...')),
                ExecuteProcess(
                    cmd=['ros2', 'launch', 'usb_cam', 'start_road.py'],
                    shell=True
                )
            ]
        )
    )

    # return LaunchDescription([container, event_handler])
    return LaunchDescription([container])

    # return ld